import {message, Modal} from 'ant-design-vue';
import {createVNode} from 'vue';
import {showToast} from 'vant';
import J<PERSON>Z<PERSON> from 'jszip';
import {saveAs} from 'file-saver';
import dayjs from 'dayjs';
import domtoimage from 'dom-to-image';

/**
 * 重置搜索项
 * @param request 需要重置的对象
 * @param dataparam  不需要重置对象中的具体字段(只作用于1级['datartParams',...])
 */
export const reset = (request: any, dataparam?: Array<string>) => {
    Object.keys(request).forEach((key) => {
        if (!dataparam) request[key] = null;
        if (dataparam && dataparam.filter((item) => item == key).length == 0) {
            request[key] = null;
        }
    });
    return request;
};

/**
 * 是否移动端
 */
export const isMobile = () => {
    let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i,
    );
    return flag;
};

/**
 * @param message 偏重的错误提醒
 * @param countDown 倒计时关闭时间
 * @param errorDetails 报错请求的全部内容（可选）
 */
export const errorModal = (message: any, countDown?: number, errorDetails?: string) => {
    const errorMessage = createVNode('div', {
        style: 'color:red;max-height:450px;overflow-y: auto;',
        id: 'error-modal-content'
    }, message);
    const titleText = '错误消息 ' + dayjs().format('YYYY-MM-DD HH:mm:ss');
    let countDownText = '';
    if (countDown) {
        countDownText = '   关闭(' + countDown + 's)';
    }

    // 根据 errorDetails 是否存在来决定按钮文字和是否显示小字
    const modalConfig: any = {
        title: titleText + countDownText,
        content: errorMessage,
        closable: true,
        onOk: async () => {
            if (errorDetails) {
                modal.destroy();
                // 延迟一小段时间确保 modal 关闭
                setTimeout(() => {
                    captureAndDownloadError(errorDetails);
                }, 1000);
            }
        }
    };

    // 如果 errorDetails 存在，则显示完整内容（包括小字提示和下载按钮）
    if (errorDetails) {
        modalConfig.content = createVNode('div', {}, [
            errorMessage,
            createVNode('div', {
                style: 'font-size: 12px; color: #999; margin-top: 16px; padding-top: 12px;'
            }, '请下载错误信息发送给系统管理员排查')
        ]);
        modalConfig.okText = '下载并关闭';
    } else {
        // 如果 errorDetails 不存在，则只显示错误信息，不显示小字提示，按钮文字为"关闭"
        modalConfig.content = errorMessage;
        modalConfig.okText = '关闭';
    }

    const modal = Modal.error(modalConfig);

    if (countDown) {
        let secondsToGo = countDown;
        const interval = setInterval(() => {
            secondsToGo -= 1;
            modal.update({
                title: titleText + '   关闭(' + secondsToGo + 's)',
            });
        }, 1000);
        setTimeout(() => {
            clearInterval(interval);
            modal.destroy();
        }, secondsToGo * 1000);
    }
};

// 截屏并下载错误信息
const captureAndDownloadError = async (errorDetails: any) => {
    try {
        // 截取整个页面
        const blob = await domtoimage.toBlob(document.body);
        const reader = new FileReader();
        const imgData = await new Promise<string>((resolve) => {
            reader.onload = () => resolve(reader.result as string);
            reader.readAsDataURL(blob);
        });

        // 创建压缩包
        const zip = new JSZip();

        // 将错误详情转换为字符串格式
        let errorDetailsString: string;
        if (typeof errorDetails === 'string') {
            errorDetailsString = errorDetails;
        } else if (errorDetails instanceof Blob) {
            errorDetailsString = await errorDetails.text();
        } else if (errorDetails instanceof ArrayBuffer) {
            const decoder = new TextDecoder();
            errorDetailsString = decoder.decode(errorDetails);
        } else {
            // 对于对象或其他类型，转换为格式化的JSON字符串
            errorDetailsString = JSON.stringify(errorDetails, null, 2);
        }

        zip.file("error-details.txt", errorDetailsString);
        zip.file("full-screenshot.png", imgData.split(',')[1], {base64: true});

        const content = await zip.generateAsync({type: "blob"});
        const timestamp = dayjs().format('YYYYMMDDHHmmss');
        saveAs(content, `error-report-${timestamp}.zip`);
    } catch (error) {
        console.error("Error capturing or downloading:", error);
        // 可以添加用户友好的错误提示
        message.error("导出错误报告失败");
    }
};

export const copyValue = (text: string | undefined) => {
    if (text) {
        navigator.clipboard
            .writeText(text)
            .then(() => {
                if (isMobile()) {
                    showToast('复制成功');
                } else {
                    message.success('复制成功');
                }
            })
            .catch((err) => {
                console.log(err, 'err');
            });
    }
};

export function isType(val: any) {
    return Object.prototype.toString.call(val).slice(8, -1);
}

export function isObject(val: any) {
    return isType(val) === 'Object';
}

/**
 * 将枚举对象转化为 [{lable:"", value:""}]形式的数组
 * @param {Object} statusEnum
 * @param {Boolean} [keyIsNumber] key是否为数字,默认false
 */
export function getEnumOptions(statusEnum: { [key: string | number]: string }, keyIsNumber = false, isAllKey = false) {
    let options: Array<{ label: string; value: string }> = [];
    if (isObject(statusEnum)) {
        for (let f in statusEnum) {
            if (statusEnum.hasOwnProperty(f) && (keyIsNumber === !isNaN(Number(f)) || isAllKey)) {
                options.push({
                    label: statusEnum[f],
                    value: f,
                });
            }
        }
    }

    return options;
}

export function removeEmptyFiled(obj: any | Array<any>) {
    if (!obj) {
        return;
    }
    if (isObject(obj)) {
        for (let f in obj) {
            if (typeof obj[f] === 'string') {
                obj[f] = obj[f].trim();
            }
            if (obj[f] === '' || obj[f] === null || obj[f] === undefined) {
                delete obj[f];
                continue;
            }
            removeEmptyFiled(obj[f]);
        }
        return;
    }
    if (Array.isArray(obj)) {
        obj.forEach((item) => {
            removeEmptyFiled(item);
        });
    }
}

//js精确加法
export function floatAdd(arg1: any, arg2: any) {
    var r1, r2, m;
    try {
        r1 = arg1.toString().split('.')[1].length;
    } catch (e) {
        r1 = 0;
    }
    try {
        r2 = arg2.toString().split('.')[1].length;
    } catch (e) {
        r2 = 0;
    }
    m = Math.pow(10, Math.max(r1, r2));
    return (arg1 * m + arg2 * m) / m;
}

//js精确 除法
export function floatDiv(arg1: any, arg2: any) {
    var t1 = 0,
        t2 = 0,
        r1,
        r2;
    try {
        t1 = arg1.toString().split('.')[1].length;
    } catch (e) {
    }
    try {
        t2 = arg2.toString().split('.')[1].length;
    } catch (e) {
    }
    r1 = Number(arg1.toString().replace('.', ''));
    r2 = Number(arg2.toString().replace('.', ''));
    return (r1 / r2) * Math.pow(10, t2 - t1);
}

// 生成唯一标识
const S4 = function () {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
};

export function guid() {
    return S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4();
}

export function mapToObj(request: Map<any, any>) {
    const obj: any = {};
    request.forEach((v, k) => {
        obj[k] = v;
    });
    return obj;
}

function isDate(obj: object) {
    return obj instanceof Date;
}

function arrrayConvertToFormData(list: Array<any>, key: string) {
    const formData = new FormData();
    list.forEach((item, index) => {
        if (isObject(item) && !isDate(item)) {
            const subData = objectConvertToFormData(item, `${key}[${index}]`);
            subData.forEach((value: any, thisKey: string) => {
                formData.append(thisKey, value);
            });
        } else if (Array.isArray(item) && item.length > 0) {
            const subFormData = arrrayConvertToFormData(item, `${key}[${index}]`);
            subFormData.forEach((value: any, thisKey: string) => {
                formData.append(thisKey, value);
            });
        } else if (item != null && item != '') {
            formData.append(`${key}[${index}]`, item);
        }
    });
    return formData;
}

export function objectConvertToFormData(object: { [key: string]: any }, key: string) {
    const formData = new FormData();
    const first = key ? key + '.' : '';

    Object.keys(object).forEach((currrentKey) => {
        if (currrentKey === 'key') {
            return;
        }
        const value = object[currrentKey];
        if (Array.isArray(value) && value.length > 0) {
            const subFormData = arrrayConvertToFormData(value, `${first}${currrentKey}`);
            subFormData.forEach((value: any, thisKey: string) => {
                formData.append(thisKey, value);
            });
        } else if (isObject(value) && value instanceof dayjs) {
            const subData = objectConvertToFormData(value, `${first}${currrentKey}`);
            subData.forEach((value: any, thisKey: string) => {
                formData.append(thisKey, value);
            });
        } else if (value != null && value != '') {
            formData.append(first + currrentKey, value);
        }
    });
    return formData;
}

export const getDealTime = (time?: string) => {
    if (!time) return '';
    const newDate = new Date();
    let date2 = new Date(time);
    let timeDiff = Math.abs(newDate.getTime() - date2.getTime());
    let day = timeDiff / (60 * 60 * 24 * 1000);
    let hour = Math.floor((timeDiff % (60 * 60 * 24 * 1000)) / (60 * 60 * 1000));
    let minute = Math.floor((timeDiff % (60 * 60 * 1000)) / (60 * 1000));
    let second = Math.floor((timeDiff % (60 * 1000)) / 1000);

    return (
        (day >= 1 ? Math.floor(day) + '天 ' : '') +
        (hour >= 10 ? hour : '0' + hour) +
        ':' +
        (minute >= 10 ? minute : '0' + minute) +
        ':' +
        (second >= 10 ? second : '0' + second)
    );
};

/**
 * 将数字按位分解为二进制位值数组
 * @param {number | undefined} num 需要分解的数字
 * @returns {number[] | undefined} 分解后的位值数组，如果输入为空则返回undefined
 * @example
 * bitValueDecompose(10) // 返回 [2, 8] (因为 10 = 2 + 8，二进制表示为 1010)
 * bitValueDecompose(7) // 返回 [1, 2, 4] (因为 7 = 1 + 2 + 4，二进制表示为 111)
 */
export const bitValueDecompose = (num: number | undefined) => {
    if (num === undefined || num === null) {
        return;
    }

    const bitValues = [1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024];

    const resultArray: number[] = [];
    bitValues.forEach((value) => {
        if ((num & value) !== 0) {
            resultArray.push(value);
        }
    });
    return resultArray;
};

// 金额大小写
export function smalltoBIG(n: any) {
    var fraction = ['角', '分'];
    var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    var unit = [
        ['元', '万', '亿'],
        ['', '拾', '佰', '仟'],
    ];
    var head = n < 0 ? '欠' : '';
    n = Math.abs(n);

    var s = '';

    for (var i = 0; i < fraction.length; i++) {
        s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
    }
    s = s || '整';
    n = Math.floor(n);

    for (var i = 0; i < unit[0].length && n > 0; i++) {
        var p = '';
        for (var j = 0; j < unit[1].length && n > 0; j++) {
            p = digit[n % 10] + unit[1][j] + p;
            n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
    }
    return (
        head +
        s
            .replace(/(零.)*零元/, '元')
            .replace(/(零.)+/g, '零')
            .replace(/^整$/, '零元整')
    );
}

// 流程编排
export function meetingProcessOrchestration(
    processNode: string = '',
    nodes: Array<any> = [],
    metaKey: string = '',
    isLog: boolean = false,
) {
    // processNode - 流程节点
    // nodes - 流程编排nodes
    // metaKey - 配置key
    // isLog - 是否打印日志

    const processList = nodes.filter((e) => e.metaKey === processNode) || []; // 节点
    if (processList.length === 0) {
        return '';
    }

    const configs = processList[0]?.configs || [];
    if (configs.length === 0) {
        return '';
    }

    const metaList = configs.filter((e) => e.metaKey === metaKey) || [];
    if (metaList.length === 0) {
        return '';
    }

    if (isLog) {
        console.log('%c [ 流程编排 ]-333', 'font-size:13px; background:pink; color:#bf2c9f;', metaKey, configs);
    }

    const metaValue = metaList[0]?.configParam || '';

    return metaValue;
}

// 数字转化数组
export function numComputedArrMethod(num: number, list: array) {
    if (!num || !list) return;

    if (list.length === 0) return;

    let array = [];
    list.map((item) => {
        if ((num & item) != 0) {
            array.push(item);
        }
    });
    return array;
}

// 数字千分位+保留两位小数
export function formatNumberThousands(number: number | string, isToFixedTwo: boolean = true) {
    if (number === null || number === undefined || number === '') {
        return '-';
    }

    // isToFixedTwo - 是否保留两位小数
    const numberValue = Number(number);

    return isToFixedTwo
        ? new Intl.NumberFormat('zh-CN', {minimumFractionDigits: 2, maximumFractionDigits: 2}).format(numberValue)
        : new Intl.NumberFormat('zh-CN').format(numberValue);
}

/**
 * 从文件路径中提取文件名，并去掉时间戳前缀
 * @param filePath 文件路径
 * @returns 处理后的文件名
 */
export function getFileNameFromPath(filePath: string): string {
    // 去除URL中的查询参数和fragment
    const cleanPath = filePath.split('?')[0].split('#')[0];

    // 提取文件名（去除路径部分）
    let fileName = cleanPath.split('/').pop() || '未知文件';
    fileName = decodeURIComponent(fileName);

    // 去掉时间戳前缀（匹配数字+连字符的格式）
    // 匹配模式：数字+连字符+文件名
    const timestampPattern = /^\d+-/;
    if (timestampPattern.test(fileName)) {
        fileName = fileName.replace(timestampPattern, '');
    }

    return fileName;
}
