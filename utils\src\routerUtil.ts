import NProgress from 'nprogress';
import 'nprogress/nprogress.css'
import { RouteRecordRaw, Router, createRouter, createWebHashHistory } from 'vue-router';
import { ResourceTypeConstant } from "@haierbusiness-front/common-libs";
import { applicationApi, resourceApi } from "@haierbusiness-front/apis";
import { typeJson } from './jsonUtil';
import { interceptUrlToken, resolveToken } from './tokenUtil';

import { storeToRefs } from "pinia";
import globalPinia from "./store/store"

import { applicationStore } from './store/applicaiton';
import { getCurrentInstance } from 'vue';

const { loginUrl, loginUser, resource, isGetResource } = storeToRefs(applicationStore(globalPinia))


/**
 * 检索动态路由模块组件权限
 * @param tree 动态路由菜单
 * @param urls  需要检索的标识(页面的url)
 */
export const findTreesByUrls = (tree: any, urls: Array<string>) => {
    let result: Array<string> = [];

    function traverse(node: any) {
        if (urls.includes(node.url)) {
            result.push(node);
        }
        if (node.children) {
            for (const child of node.children) {
                traverse(child);
            }
        }
    }

    for (const node of tree) {
        traverse(node);
    }
    return result;
}


/**
 * 获取router
 */
export const getCurrentRouter = () => {
    return getCurrentInstance()!.appContext.app.config.globalProperties.$hbRouter
}

/**
 * 获取route
 */
export const getCurrentRoute = () => {
    const router = getCurrentRouter()
    return router.currentRoute.value
}

/**
 * 初始化路由
 * token 以及资源的解析
 * @param applicationCode  应用编码
 * @param sourceTypes 需要查询的资源类型, 默认[ResourceTypeConstant.PAGE.type, ResourceTypeConstant.PAGE_MENU.type, ResourceTypeConstant.PAGE_GROUP.type, ResourceTypeConstant.WIDGET.type, ResourceTypeConstant.MANAGE_APPLICATION.type, ResourceTypeConstant.APPLICATION.type]
 * @param needOwnResource 是否需要查询登录人拥有的资源
 * @param modules  模块
 * @returns
 */
export const baseRouterConstructor = async (applicationCode: string, modules: any, needOwnResource?: boolean, sourceTypes?: number[], whiteList: RouteRecordRaw[] = []): Promise<Router> => {
    // 获取应用登录地址
    loginUrl.value = await applicationApi.loginUrl(
        {
            "applicationCode": applicationCode
        }
    ).then(it => {
        return it
    });

    // 截取token 生成登录用户,存到stroe中
    interceptUrlToken()
    loginUser.value = resolveToken()

    let routes: RouteRecordRaw[] = []
    if (needOwnResource) {
        resource.value = await searchOwnTrees(applicationCode, sourceTypes)
        routes = routerInit(resource.value, modules)
        isGetResource.value = true
    }

    if (whiteList && whiteList.length > 0) {
        routes = [...routes, ...whiteList]
    }

    NProgress.configure({
        speed: 200,
        minimum: 0.02,
        trickleSpeed: 200,
        showSpinner: false
    });

    const router = createRouter({
        routes,
        history: createWebHashHistory(),
        scrollBehavior() {
            return { top: 0 };
        }
    });

    router.beforeEach(async (to, from, next) => {
        NProgress.start();
        // 判断是否存在白名单中
        let list = flatten(whiteList)

        const index = list.findIndex(o => o === to.path)
        if (index === -1 && !isGetResource.value && to.path !== "/404" && to.path !== "/") {
            // 白名单中不存在且未获取资源则执行资源获取
            resource.value = await searchOwnTrees(applicationCode, sourceTypes)
            routes = routerInit(resource.value, modules)
            isGetResource.value = true
            routes.forEach((route) => {
                router.addRoute(route)
            })
        }
        next()
    })

    router.afterEach((to, from) => {
        NProgress.done(true);
        
        // 强制滚动到顶部 - 使用多种方法确保生效
        setTimeout(() => {
            const scrollContainers = document.querySelectorAll('.ant-layout-content, .ant-layout, [data-scroll-container]');
            scrollContainers.forEach(container => {
                if (container instanceof HTMLElement) {
                    container.scrollTop = 0;
                }
            });
        }, 50);
        
        let routers = router.getRoutes()
        const index = routers.findIndex(o => {
            let rpath = o.path
            let tpath = to.path
            if (rpath.charAt(rpath.length - 1) !== "/") {
                rpath = rpath + "/"
            }
            if (tpath.charAt(tpath.length - 1) !== "/") {
                tpath = tpath + "/"
            }
            return tpath === rpath
        })
        if (index === -1 && to.path !== "/404") {
            // 路由不存在跳转404页面
            router.push({ path: "/404" })
        }
    })

    // 初始化全局静态路由
    router.addRoute({
        path: '/404',
        component: () => import('@haierbusiness-front/components/error/404.vue')
    })
    return router
}

const flatten = (whiteList: RouteRecordRaw[] = []) => {
    let list: string[] = []
    if (whiteList && whiteList.length > 0) {
        whiteList.map((item) => {
            list.push(item.path)
            if (item.children) {
                const child = flatten(item.children)
                list = [...list, ...child]
            }
        })
        return list
    } else
        return [] as string[]
}

const searchOwnTrees = async (applicationCode: string, sourceTypes?: number[]) => {
    return await resourceApi.searchOwnTrees(
        {
            "applicationCode": applicationCode,
            "parentId": 0,
            "types": sourceTypes || [ResourceTypeConstant.PAGE.type, ResourceTypeConstant.PAGE_MENU.type, ResourceTypeConstant.PAGE_GROUP.type, ResourceTypeConstant.WIDGET.type, ResourceTypeConstant.MANAGE_APPLICATION.type, ResourceTypeConstant.APPLICATION.type]
        }
    ).then(it => {
        return it
    });
}

export const routerInit = (resource: any[], modules: any): RouteRecordRaw[] => {
    const routes: RouteRecordRaw[] = []
    for (const item of resource) {
        if (item.type === ResourceTypeConstant.PAGE.type || item.type === ResourceTypeConstant.PAGE_MENU.type) {
            const url = item.url.indexOf('?') > 0 ? item.url.substring(0, item.url.indexOf('?')) : item.url
            const routerItem: RouteRecordRaw = {
                path: url,
                name: item.id.toString(),
                component: modules[item.position],
                meta: { title: item.name, keepAlive: item.keepAlive },
                props: route => {
                    // 消除异常
                    if (JSON.stringify(route.query) === "{}") {
                        return {}
                    }
                    return { query: route.query }
                },
                children: (() => {
                    if (item.children) {
                        return routerInit(item.children, modules)
                    } else {
                        return []
                    }
                })()
            }
            routes.push(routerItem)
        } else if (item.type === ResourceTypeConstant.MANAGE_APPLICATION.type || item.type === ResourceTypeConstant.APPLICATION.type) {
            let hashUrl = item.url
            if (hashUrl.includes("#/")) {
                hashUrl = hashUrl.substring(hashUrl.indexOf("/#/")).substring(2)
            }
            if (hashUrl.includes("?")) {
                hashUrl = hashUrl.substring(0, hashUrl.indexOf("?"))
            }
            if (!hashUrl.endsWith("/")) {
                hashUrl = hashUrl + "/"
            }
            const routerItem: RouteRecordRaw = {
                path: hashUrl,
                name: item.id.toString(),
                component: modules[item.position],
                meta: { title: item.name, keepAlive: item.keepAlive },
                props: route => {
                    if (JSON.stringify(route.query) === "{}") {
                        return {}
                    }
                    return { query: route.query }
                },
                children: (() => {
                    if (item.children) {
                        return routerInit(item.children, modules)
                    } else {
                        return []
                    }
                })()
            }
            routes.push(routerItem)
        } else if (item.type === ResourceTypeConstant.PAGE_GROUP.type) {
            routes.push(...routerInit(item.children, modules))
        }
    }
    return routes
}

export const routerParam = (data: any): string => {
    if (data instanceof Set) {
        return encodeURIComponent(JSON.stringify([...data]))
    } else if (data instanceof Object) {
        return encodeURIComponent(JSON.stringify(data))
    } else {
        return data
    }
}

export const resolveParam = <T>(data: string): any => {
    const dataStr = decodeURIComponent(data)
    if (!typeJson(dataStr)) {
        return dataStr as T
    }
    return JSON.parse(decodeURIComponent(data)) as T
}