{"name": "@haierbusiness-front/utils", "main": "index.ts", "private": true, "version": "0.0.0", "type": "module", "devDependencies": {"@types/nprogress": "^0.2.0", "typescript": "^4.9.3", "vant": "^4.6.1", "vite": "^4.1.0"}, "dependencies": {"@haierbusiness-front/apis": "workspace:^0.0.0", "@haierbusiness-front/common-libs": "workspace:^0.0.0", "@haierbusiness-front/components": "workspace:^0.0.0", "ant-design-vue": "4.x", "dayjs": "1.11.9", "dom-to-image": "^2.6.0", "file-saver": "^2.0.5", "jszip": "^3.10.1", "nprogress": "^0.2.0", "pinia": "^2.0.33", "pinia-plugin-persistedstate": "^3.2.1", "vue": "^3.2.45", "vue-router": "4"}}