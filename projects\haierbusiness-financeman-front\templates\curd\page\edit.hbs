<script lang="ts" setup >
import {
  Select as hSelect, SelectOption as hSelectOption, Modal as hModal, Form as hForm, FormItem as hFormItem,
  Input as hInput, CheckboxGroup as hCheckboxGroup, Checkbox as hCheckbox, Row as hRow, Col as hCol, DatePicker as hDatePicker, message,
  Upload as hUpload, RadioGroup as hRadioGroup, Radio as hRadio, InputNumber as hInputNumber, <PERSON>ton as hButton, Switch as hSwitch
} from 'ant-design-vue';
import { computed, ref, watch, onMounted } from "vue";
import { useRoute, useRouter } from 'vue-router'
import type { Ref } from "vue";
import {
  I{{ properCase modelName }}, HeaderConstant
} from '@haierbusiness-front/common-libs';
import { useRequest } from 'vue-request';
import { fileApi, {{ camelCase modelName }}Api } from '@haierbusiness-front/apis';
import type { UploadChangeParam, UploadProps } from 'ant-design-vue';
import PlusOutlined from '@ant-design/icons-vue/PlusOutlined';
import LoadingOutlined from '@ant-design/icons-vue/LoadingOutlined';
import { UploadOutlined } from '@ant-design/icons-vue'
import { toNumber } from "lodash-es"
import router from '../../router'
import { loadDataFromLocal } from '@haierbusiness-front/utils/src/storageUtil'

// const router = useRouter()
const route = useRoute();

const currentRouter = ref()

const from = ref();
const confirmLoading = ref(false);
const id = ref<number>()

onMounted(async () => {
    currentRouter.value = await router
    const currentId = currentRouter.value.currentRoute.query?.id
    id.value = toNumber(currentId)
    if (id.value) {
      await get(id.value)
    }
    else {
      {{ camelCase modelName }}.value = {}
    }
})

watch(() => currentRouter.value?.currentRoute.query, (newValue, oldValue) => {
  id.value = toNumber(newValue.id)
  if (id.value) {
    get(id.value)
  } 
  else {
    {{ camelCase modelName }}.value = {}
  }
})

const get = async (id: number) => {
  const data = await {{ camelCase modelName }}Api.get(id)
  if(data && data.id) {
    {{ camelCase modelName }}.value = data
  } 
}

const rules = {

};

const {{ camelCase modelName }} = ref<I{{ properCase modelName }}>({});

//#region 上传相关，没有请删除

const token = loadDataFromLocal(HeaderConstant.TOKEN_KEY.key, false)
const baseUrl = import.meta.env.VITE_BUSINESS_URL

const fileList = ref<Array<any>>([]);
const loading = ref<boolean>(false);
const imageUrl = ref<string>('');

const beforeUpload = (file: any) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg'
  if (!isJpgOrPng) {
    message.error('只能上传格式为png/jpg/jpeg的文件！')
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('Image must smaller than 2MB!');
  }
  return isJpgOrPng && isLt2M;
};

const upload = async (options:any) => {
    loading.value = true;
    const formData = new FormData()
    formData.append('file', options.file)
    const res = await fileApi.upload(formData)
    const file = {
        ...options.file,
        name: options.file.name,
        url: baseUrl + res.path
    }
    loading.value = false;
    fileList.value = [...fileList.value, file]
    imageUrl.value = baseUrl + res.path
    {{ camelCase modelName }}.value.imgUrl = baseUrl + res.path
    
    options.onProgress(100)
    options.onSuccess(res, options.file)  //这里必须加上这个，不然会一直显示一个正在上传中的框框
}

//#endregion




const handleOk = () => {
  confirmLoading.value = true;
  from.value.validate()
    .then(() => {
      const data = {
        ...{{ camelCase modelName }}.value,
      }

      if (data.id) {
        {{ camelCase modelName }}Api.edit(data).then(res => {
          message.success(`编辑成功!`);
          currentRouter.value.push({ path: "/supportman/{{ camelCase modelName }}/index"})
        })
      } else {
        {{ camelCase modelName }}Api.save(data).then(res => {
          message.success(`新增成功!`);
          currentRouter.value.push({ path: "/supportman/{{ camelCase modelName }}/index"})
        })
      }
      confirmLoading.value = false;
    })
    .catch(() => {
      confirmLoading.value = false;
    });
};

</script>

<template>
  <div style="background-color: #ffff;height: 100%;width: 100%;padding: 10px 10px 0px 10px;overflow: auto;">
    <h-form ref="from" :model="{{ camelCase modelName }}" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules" @finish="handleOk">
        <div class="submit-btn">
          <h-button type="primary" shape="round" html-type="submit" class="sub-btn">提交</h-button>
        </div>
    </h-form>
  </div>
    
</template>


<style lang="less" scoped>

.submit-btn {
  width: 100%;
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
}

.sub-btn {
  width: 200px;
  
}

</style>