module.exports = {
    description: "创建标准CURD",
    prompts: [
        {
            type: 'input',
            message: '请输入模型名称(用-分隔单词, 统一小写),勿与之前重复,然后点击回车.如：user',
            name:'modelName'
        },
        {
            type: "input",
            message: "请输入模型标识名称。建议中文，如：用户",
            name: "labelName",
        },
        {
            type: "confirm",
            name: "isRoot",
            message: "当前模型是否要放在page中的某个文件夹下？选择Y会在/page/*/下创建文件夹，选择N会在/page下创建",
            default: true,
        },
        {
            type: "input",
            name: "parentName",
            message: "请输入该组件所属的文件夹名称",
            when: (data) => {
                return data.isRoot;
            },
        },
        {
            type: "confirm",
            name: "isPackage",
            message: "当前业务组件是否为主包主入口组件？",
            default: true,
        },
        {
            type: "input",
            name: "packageName",
            message: "请输入该组件属于主包名称",
            default: "",
            when: (data) => {
                return !data.isPackage;
            },
        },
        {
            type: "confirm",
            name: "isEdit",
            message: "是否需要编辑功能？",
            default: true,
        },
        {
            type: "confirm",
            name: "isDialog",
            message: "编辑是否采用弹窗形式？（Y生成弹窗组件，N生成单独页面）",
            default: true,
            when: (data) => {
                return data.isEdit;
            },
        },
        {
            type: "input",
            name: "dialogWidth",
            message: "请输入弹窗宽度，默认520",
            default: 520,
            when: (data) => {
                return data.isDialog;
            },
        },

    ],
    actions:(data) => {
        const { modelName, labelName, isPackage, packageName, isRoot, parentName, dialogWidth, isEdit, isDialog } = data
        let listPath = isPackage
            ? `${modelName}/index.vue`
            : `${packageName}/${modelName}s.vue`
        let editDialogPath = isPackage
            ? `${modelName}/edit-dialog.vue`
            : `${packageName}/${modelName}-edit-dialog.vue`
        let editPath = isPackage
            ? `${modelName}/edit.vue`
            : `${packageName}/${modelName}-edit.vue`

        isRoot && (listPath = parentName + '/' + listPath)
        isRoot && (editDialogPath =  parentName + '/' + editDialogPath)
        isRoot && (editPath =  parentName + '/' + editPath)

        //狗血 plop中的双大括号和vue的冲突，没找到如何解决的办法，只能利用plop把大括号加上去
        const start = '{{'
        const end = '}}'

        let addFile = [
            {
                type: "add",
                path: `../../apis/src/supportman/${data.modelName}.ts`,
                templateFile: "templates/curd/api/api.hbs",
                data: {
                    modelName,
                    labelName,
                },
            },
            {
                type: "add",
                path: `../../apis/src/supportman/index.ts`,
                templateFile: "templates/curd/api/index.hbs",
                data: {
                    modelName
                },
            },
            {
                type: "add",
                path: `../../common-libs/src/micesupportman/model/${data.modelName}Model.ts`,
                templateFile: "templates/curd/type/type.hbs",
                data: {
                    modelName,
                },
            },
            {
                type: "append",
                path: `../../common-libs/src/micesupportman/index.ts`,
                templateFile: "templates/curd/type/index.hbs",
                data: {
                    modelName,
                },
            },
            {
                type: "add",
                path: `src/page/${listPath}`,
                templateFile: "templates/curd/page/index.hbs",
                data: {
                    modelName,
                    labelName,
                    editDialogPath,
                    start,
                    end,
                    isDialog,
                    isEdit
                },
            },
            {
                type: "append",
                path: `../../apis/index.ts`,
                templateFile: "templates/curd/api/export.hbs",
                data: {
                    modelName,
                },
            },
            {
                type: "append",
                path: `../../common-libs/index.ts`,
                templateFile: "templates/curd/api/export.hbs",
                data: {
                    modelName,
                },
            }
        ]

        if(isEdit) {
            isDialog && addFile.push( {
                type: "add",
                path: `src/page/${editDialogPath}`,
                templateFile: "templates/curd/page/edit-dialog.hbs",
                data: {
                    modelName,
                    labelName,
                    dialogWidth
                },
            })
            !isDialog && addFile.push( {
                type: "add",
                path: `src/page/${editPath}`,
                templateFile: "templates/curd/page/edit.hbs",
                data: {
                    modelName,
                    labelName
                },
            })
        }

        return addFile
    }
}